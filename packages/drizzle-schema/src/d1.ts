// src/db/schema.ts
import { relations, sql } from "drizzle-orm";
import {
  index,
  integer,
  primaryKey,
  sqliteTable,
  text,
} from "drizzle-orm/sqlite-core";
import { longId } from "@repo/utils/nanoid";

// Users (Kunden deines Dienstes)
export const users = sqliteTable("users", {
  id: text("id").primaryKey(), // z.B. auth0|xyz..., oder eigene UUID
  email: text("email").unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  avatar: text("avatar"),
  createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
  updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
    sql`(unixepoch('now', 'subsec') * 1000)`
  ),
});

export const usersRelations = relations(users, ({ many }) => ({
  organizations: many(organizationMembers),
}));

// Organizations/Teams table
export const organizations = sqliteTable("organizations", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  logo: text("logo"),
  paddle_customer_id: text("paddle_customer_id"),
  paddle_subscription_id: text("paddle_subscription_id"),
  plan: text("plan", { enum: ["standard", "pro", "enterprise"] })
    .notNull()
    .default("standard"),
  status: text("status", {
    enum: ["active", "trialing", "past_due", "unpaid", "canceled", "inactive"],
  })
    .notNull()
    .default("inactive"),
  current_period_end: integer("current_period_end", { mode: "timestamp" }),
  billingEmail: text("billing_email"),
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .default(sql`(unixepoch())`),
});

export const organizationsRelations = relations(organizations, ({ many }) => ({
  members: many(organizationMembers),
  projects: many(projects),
}));

// Organization members - many-to-many relationship between users and organizations
export const organizationMembers = sqliteTable(
  "organization_members",
  {
    organizationId: text("organization_id")
      .notNull()
      .references(() => organizations.id, { onDelete: "cascade" }),
    userId: text("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    role: text("role", { enum: ["owner", "admin", "member"] })
      .notNull()
      .default("member"),
    invitedBy: text("invited_by").references(() => users.id),
    invitedAt: integer("invited_at", { mode: "timestamp" }),
    joinedAt: integer("joined_at", { mode: "timestamp" })
      .notNull()
      .default(sql`(unixepoch())`),
    createdAt: integer("created_at", { mode: "timestamp" })
      .notNull()
      .default(sql`(unixepoch())`),
  },
  (table) => [primaryKey({ columns: [table.organizationId, table.userId] })]
);

export const organizationMembersRelations = relations(
  organizationMembers,
  ({ one }) => ({
    organization: one(organizations, {
      fields: [organizationMembers.organizationId],
      references: [organizations.id],
    }),
    user: one(users, {
      fields: [organizationMembers.userId],
      references: [users.id],
    }),
  })
);

export const projects = sqliteTable(
  "projects",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => longId()),
    organizationId: text("organization_id")
      .notNull()
      .references(() => organizations.id, { onDelete: "cascade" }),
    createdBy: text("user_id")
      .notNull()
      .references(() => users.id), // User who created the feed
    name: text("name").notNull(),
    description: text("description"),
    status: text("status", { enum: ["active", "paused", "archived"] })
      .notNull()
      .default("active"),
    createdAt: integer("created_at", { mode: "timestamp" })
      .notNull()
      .default(sql`(unixepoch())`),
    updatedAt: integer("updated_at", { mode: "timestamp" })
      .notNull()
      .default(sql`(unixepoch())`),
  },
  (table) => ({
    organizationIdx: index("project_organization_idx").on(table.organizationId),
  })
);

export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [projects.organizationId],
    references: [organizations.id],
  }),
  feeds: many(feeds),
  platformConnections: many(platformConnections),
}));

// Feeds (vom Nutzer erstellt)
export const feeds = sqliteTable(
  "feeds",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => longId()),
    projectId: text("project_id")
      .notNull()
      .references(() => projects.id, { onDelete: "cascade" }),
    createdBy: text("user_id").notNull(), // User who created the feed
    name: text("name").notNull(),
    description: text("description"),
    status: text("status", { enum: ["active", "paused", "archived"] })
      .notNull()
      .default("active"),
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    lastActivity: integer("last_activity", { mode: "timestamp_ms" }),
  },
  (table) => ({
    projectIdx: index("feed_project_idx").on(table.projectId),
  })
);

export const feedsRelations = relations(feeds, ({ one, many }) => ({
  project: one(projects, {
    fields: [feeds.projectId],
    references: [projects.id],
  }),
  connections: many(feedConnections),
  apiKeys: many(apiKeys),
}));

// Platform Connections (OAuth Verknüpfungen zu sozialen Netzwerken)
export const platformConnections = sqliteTable(
  "platform_connections",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => longId()),
    projectId: text("project_id")
      .notNull()
      .references(() => projects.id, { onDelete: "cascade" }),
    platform: text("platform", {
      enum: ["instagram", "instagram_business", "facebook", "tiktok"],
    }).notNull(),
    platformAccountId: text("platform_account_id"), // z.B. Instagram User ID, Facebook Page ID
    platformAccountName: text("platform_account_name"), // z.B. Instagram @username, FB Page Name
    accessTokenEncrypted: text("access_token_encrypted"), // Tokens IMMER verschlüsselt
    refreshTokenEncrypted: text("refresh_token_encrypted"), // Nur falls relevant (IG hat keine Refresh Tokens für Long-Lived)
    scopes: text("scopes"), // Gewährte Berechtigungen als Text (z.B. komma-separiert)
    status: text("status", {
      enum: ["active", "inactive", "reauth_needed", "expired"],
    }).default("active"), // Status der Verbindung
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    tokenExpiresAt: integer("token_expires_at", { mode: "timestamp_ms" }), // Ungefähres Ablaufdatum (falls bekannt)
    lastCheckedAt: integer("last_checked_at", { mode: "timestamp_ms" }), // Token-Gültigkeit zuletzt geprüft
    lastPolledAt: integer("last_polled_at", { mode: "timestamp_ms" }), // Nur für Basic Display Polling relevant
  },
  (table) => ({
    // Index für schnelle Suche nach Connections pro Project
    projectIdx: index("connection_feed_idx").on(table.projectId),
    // Sicherstellen, dass eine Plattform pro Feed nur einmal vorkommt (optional, je nach Logik)
    // feedPlatformUnique: uniqueIndex('feed_platform_unique_idx').on(table.feedId, table.platform, table.platformAccountId),
  })
);

export const platformConnectionsRelations = relations(
  platformConnections,
  ({ one, many }) => ({
    project: one(projects, {
      fields: [platformConnections.projectId],
      references: [projects.id],
    }),
    posts: many(posts),
    generatedLinks: many(generatedLinks),
  })
);

// Platform Connections (OAuth Verknüpfungen zu sozialen Netzwerken)
export const feedConnections = sqliteTable(
  "platform_connections",
  {
    feedId: text("feed_id")
      .notNull()
      .references(() => feeds.id, { onDelete: "cascade" }), // Beziehung zu feeds
    platformConnectionId: text("platform_connection_id")
      .notNull()
      .references(() => platformConnections.id, { onDelete: "cascade" }), // Beziehung zu platform_connections
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
  },
  (table) => ({
    feedConnectionIdx: primaryKey({
      columns: [table.feedId, table.platformConnectionId],
    }),
  })
);

export const feedConnectionsRelations = relations(
  feedConnections,
  ({ one }) => ({
    feed: one(feeds, {
      fields: [feedConnections.feedId],
      references: [feeds.id],
    }),
    platformConnection: one(platformConnections, {
      fields: [feedConnections.platformConnectionId],
      references: [platformConnections.id],
    }),
  })
);

// API Keys (Zugriff auf einen spezifischen Feed)
export const apiKeys = sqliteTable(
  "api_keys",
  {
    key: text("key").primaryKey(), // Der API Key selbst (Hash wäre sicherer)
    feedId: text("feed_id")
      .notNull()
      .references(() => feeds.id, { onDelete: "cascade" }), // Beziehung zu feeds
    createdBy: text("created_by")
      .notNull()
      .references(() => users.id),
    description: text("description"),
    status: text("status", { enum: ["active", "inactive", "revoked"] }).default(
      "active"
    ),
    keyPreview: text("key_preview").notNull(),
    createdAt: integer("created_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    updatedAt: integer("updated_at", { mode: "timestamp_ms" }).default(
      sql`(unixepoch('now', 'subsec') * 1000)`
    ),
    expiresAt: integer("expires_at", { mode: "timestamp_ms" }),
    lastUsedAt: integer("last_used_at", { mode: "timestamp_ms" }),
  },
  (table) => ({
    feedIdx: index("apikey_feed_idx").on(table.feedId),
  })
);

export const apiKeysRelations = relations(apiKeys, ({ one }) => ({
  feed: one(feeds, {
    fields: [apiKeys.feedId],
    references: [feeds.id],
  }),
}));

// Posts (Verweist jetzt auf platformConnections)
export const posts = sqliteTable(
  "posts",
  {
    platformConnectionId: text("platform_connection_id")
      .notNull()
      .references(() => platformConnections.id, { onDelete: "cascade" }), // Beziehung
    mediaId: text("media_id").notNull(), // ID des Mediums auf der Plattform
    likeCount: integer("like_count").default(0),
    commentsCount: integer("comments_count").default(0),
    caption: text("caption"),
    mediaUrl: text("media_url"),
    mediaType: text("media_type"), // Z.B. IMAGE, VIDEO, CAROUSEL_ALBUM
    permalink: text("permalink"), // Link zum Post
    timestamp: integer("timestamp", { mode: "timestamp_ms" }), // Erstellungszeitpunkt des Posts auf der Plattform
    lastWebhookUpdate: integer("last_webhook_update", { mode: "timestamp_ms" }),
    lastFetched: integer("last_fetched", { mode: "timestamp_ms" }),
  },
  (table) => ({
    connectionMediaIdx: primaryKey({
      columns: [table.platformConnectionId, table.mediaId],
    }),
    connectionTimestampIdx: index("connection_timestamp_idx").on(
      table.platformConnectionId,
      table.timestamp
    ), // Für Sortierung
  })
);

export const postsRelations = relations(posts, ({ one }) => ({
  platformConnection: one(platformConnections, {
    fields: [posts.platformConnectionId],
    references: [platformConnections.id],
  }),
}));

// Generated links for social media connections
export const generatedLinks = sqliteTable(
  "generated_links",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => longId()),
    platformConnectionId: text("plattform_connection_id")
      .notNull()
      .references(() => platformConnections.id, { onDelete: "cascade" }),
    token: text("token").notNull().unique(),
    name: text("name").notNull(),
    description: text("description"),
    redirectUrl: text("redirect_url"),
    expiresAt: integer("expires_at", { mode: "timestamp" }),
    isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
    createdBy: text("created_by")
      .notNull()
      .references(() => users.id),
    createdAt: integer("created_at", { mode: "timestamp_ms" })
      .notNull()
      .default(sql`(unixepoch('now', 'subsec') * 1000)`),
    updatedAt: integer("updated_at", { mode: "timestamp_ms" })
      .notNull()
      .default(sql`(unixepoch('now', 'subsec') * 1000)`),
  },
  (table) => ({
    platformConnectionIdx: index("generated_link_plattform_connection_idx").on(
      table.platformConnectionId
    ),
    expiresAt: index("generated_link_expires_at_idx").on(table.expiresAt),
    isActive: index("generated_link_is_active_idx").on(table.isActive),
  })
);

export const generatedLinksRelations = relations(generatedLinks, ({ one }) => ({
  platformConnection: one(platformConnections, {
    fields: [generatedLinks.platformConnectionId],
    references: [platformConnections.id],
  }),
}));
