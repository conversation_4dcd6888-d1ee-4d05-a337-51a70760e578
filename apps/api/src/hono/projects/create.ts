import { Context } from "hono";
import { AppContext } from "../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../analytics-utils";
import { fixUserAndOrgTable } from "../manage/account/fix";

export const createProject = async (
  c: Context<AppContext, "/manage/projects", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const body = await c.req.json();
  const feedName = body.name;
  const feedDescription = body.description;
  const userId = c.var.user?.userId;

  if (!feedName || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const db = getDbClient(c.env.DB);
  try {
    const resp = await db
      .insert(schema.projects)
      .values({
        createdBy: c.var.user?.userId,
        name: feedName,
        organizationId: organizationId,
        description: feedDescription,
        status: "active",
      })
      .run();
    return c.json({ id: resp.lastInsertRowId });
  } catch (e) {
    console.error(`Failed to add feed for org ${organizationId}:`, e);
    logErrorToAnalytics(c.env, "FEED_ADD_ERROR", `Failed to add feed`, {
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to add feed",
    });
  }
};
