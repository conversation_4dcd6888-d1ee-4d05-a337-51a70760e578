import { HTTPException } from "hono/http-exception";
import {
  getConnectionDetails,
  getDbClient,
  isConnectionOwnedByOrganization,
} from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext, SyncQueueMessage } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";
import * as schema from "@repo/drizzle-schema/d1";
import { arrayContained, arrayContains, eq, sql } from "drizzle-orm";
import { fixUserAndOrgTable } from "../account/fix";

export const getAllFeeds = async (
  c: Context<AppContext, "/manage/feed/all", BlankInput>
) => {
  console.log("thiS");
  const page = Math.min(
    Math.max(1, parseInt(c.req.query("page") || "1", 10)),
    1000
  );

  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  );

  console.log("PAGEE; LIMIT", page, limit);

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);
  try {
    const feedss = await db.query.feeds.findMany({
      columns: {
        id: true,
        name: true,
        description: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        lastActivity: true,
      },
      with: {
        project: {
          columns: {
            id: true,
            organizationId: true,
          },
        },
        connections: {
          with: {
            platformConnection: {
              columns: {
                platformAccountName: true,
                platform: true,
                status: true,
              },
            },
          },
        },
      },
      where: (feedsTable, { eq, and }) => {
        return and(
          eq(schema.projects.organizationId, organizationId), // Direkt auf schema.projects zugreifen
          eq(feedsTable.projectId, schema.projects.id) // UND die Join-Bedingung hinzufügen
        );
      },
      limit: limit,
      offset: page ? limit * (page - 1) : 0,
    });

    const feeds = await db
      .select({
        id: schema.feeds.id,
        name: schema.feeds.name,
        description: schema.feeds.description,
        status: schema.feeds.status,
        createdAt: schema.feeds.createdAt,
        updatedAt: schema.feeds.updatedAt,
        lastActivity: schema.feeds.lastActivity,
        connections: {
          id: schema.platformConnections.id,
          platformAccountName: schema.platformConnections.platformAccountName,
          platform: schema.platformConnections.platform,
          status: schema.platformConnections.status,
        },
      })
      .from(schema.feeds)
      .where(eq(schema.projects.organizationId, organizationId))
      .leftJoin(schema.projects, eq(schema.feeds.projectId, schema.projects.id))
      .leftJoin(
        schema.feedConnections,
        eq(schema.feedConnections.feedId, schema.feeds.id)
      )
      .leftJoin(
        schema.platformConnections,
        eq(
          schema.platformConnections.id,
          schema.feedConnections.platformConnectionId
        )
      )
      .groupBy(
        schema.feeds.id,
        schema.feeds.name,
        schema.feeds.description,
        schema.feeds.status,
        schema.feeds.createdAt,
        schema.feeds.updatedAt,
        schema.feeds.lastActivity
      )
      .limit(limit)
      .offset(page ? limit * (page - 1) : 0);

    console.log(feeds.length, page ? limit * (page - 1) : 0);
    return c.json(feeds);
  } catch (e) {
    console.error(`Failed to fetch feeds for org ${organizationId}:`, e);
    logErrorToAnalytics(c.env, "FEED_FETCH_ERROR", `Failed to fetch feeds`, {
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, {
      message: "Failed to fetch feeds",
    });
  }
};

export const addFeed = async (
  c: Context<AppContext, "/manage/feed", BlankInput>
) => {
  console.log("WE ARE HERE");
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const body = await c.req.json();
  const feedName = body.name;
  const feedDescription = body.description;
  const projectId = body.projectId;
  const userId = c.var.user?.userId;

  if (!feedName || !userId)
    throw new HTTPException(400, { message: "Feed name required" });

  const feedId = crypto.randomUUID();

  const db = getDbClient(c.env.DB);
  try {
    await db
      .insert(schema.feeds)
      .values({
        id: feedId,
        createdBy: c.var.user?.userId,
        projectId: projectId,
        name: feedName,
        description: feedDescription,
        status: "active",
      })
      .run();
    return c.json({ id: feedId });
  } catch (e) {
    console.error(`Failed to add feed for org ${organizationId}:`, e);
    logErrorToAnalytics(c.env, "FEED_ADD_ERROR", `Failed to add feed`, {
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to add feed",
    });
  }
};

export default getAllFeeds;
