import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const getFeedCount = async (
  c: Context<AppContext, "/manage/feed/count", BlankInput>
) => {
  console.log("Wir sind hier");
  const organizationId = c.var.organizationId;
  if (!organizationId) {
    console.log("NO ORGANIZATION");
    throw new HTTPException(403, {
      message: "Organization context required",
    });
  }

  console.log("WHAT");

  const db = getDbClient(c.env.DB);
  try {
    const feeds = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.feeds)
      .where(eq(schema.feeds.organizationId, organizationId))
      .get();
    const activeFeeds = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.feeds)
      .where(
        and(
          eq(schema.feeds.status, "active"),
          eq(schema.feeds.organizationId, organizationId)
        )
      )
      .get();
    return c.json({
      feeds: feeds?.count ?? 0,
      activeFeeds: activeFeeds?.count ?? 0,
    });
  } catch (e) {
    console.error(`Failed to fetch feeds for org ${organizationId}:`, e);
    logErrorToAnalytics(c.env, "FEED_FETCH_ERROR", `Failed to fetch feeds`, {
      organizationId,
      error: String(e),
    });
    throw new HTTPException(500, {
      message: "Failed to fetch feeds",
    });
  }
};
export default getFeedCount;
