				import worker, * as OTHER_EXPORTS from "/Users/<USER>/Documents/ENTWICKLUNGSUMGEBUNG/socialfeed/apps/api/src/index.ts";
				import * as __MIDDLEWARE_0__ from "/Users/<USER>/Documents/ENTWICKLUNGSUMGEBUNG/socialfeed/node_modules/.pnpm/wrangler@4.20.3_@cloudflare+workers-types@4.20250618.0_bufferutil@4.0.9/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts";
import * as __MIDDLEWARE_1__ from "/Users/<USER>/Documents/ENTWICKLUNGSUMGEBUNG/socialfeed/node_modules/.pnpm/wrangler@4.20.3_@cloudflare+workers-types@4.20250618.0_bufferutil@4.0.9/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts";

				export * from "/Users/<USER>/Documents/ENTWICKLUNGSUMGEBUNG/socialfeed/apps/api/src/index.ts";
				const MIDD<PERSON>WARE_TEST_INJECT = "__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__";
				export const __INTERNAL_WRANGLER_MIDDLEWARE__ = [
					
					__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default
				]
				export default worker;