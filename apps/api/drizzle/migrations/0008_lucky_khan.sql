CREATE TABLE `projects` (
	`id` text PRIMARY KEY NOT NULL,
	`organization_id` text NOT NULL,
	`user_id` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`status` text DEFAULT 'active' NOT NULL,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL,
	FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE INDEX `project_organization_idx` ON `projects` (`organization_id`);--> statement-breakpoint
PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_generated_links` (
	`id` text PRIMARY KEY NOT NULL,
	`plattform_connection_id` text NOT NULL,
	`token` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`redirect_url` text,
	`expires_at` integer,
	`is_active` integer DEFAULT true NOT NULL,
	`created_by` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`plattform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_generated_links`("id", "plattform_connection_id", "token", "name", "description", "redirect_url", "expires_at", "is_active", "created_by", "created_at", "updated_at") SELECT "id", "plattform_connection_id", "token", "name", "description", "redirect_url", "expires_at", "is_active", "created_by", "created_at", "updated_at" FROM `generated_links`;--> statement-breakpoint
DROP TABLE `generated_links`;--> statement-breakpoint
ALTER TABLE `__new_generated_links` RENAME TO `generated_links`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `generated_links_token_unique` ON `generated_links` (`token`);--> statement-breakpoint
CREATE INDEX `generated_link_plattform_connection_idx` ON `generated_links` (`plattform_connection_id`);--> statement-breakpoint
CREATE INDEX `generated_link_expires_at_idx` ON `generated_links` (`expires_at`);--> statement-breakpoint
CREATE INDEX `generated_link_is_active_idx` ON `generated_links` (`is_active`);--> statement-breakpoint
CREATE TABLE `__new_platform_connections` (
	`id` text PRIMARY KEY NOT NULL,
	`project_id` text NOT NULL,
	`platform` text NOT NULL,
	`platform_account_id` text,
	`platform_account_name` text,
	`access_token_encrypted` text,
	`refresh_token_encrypted` text,
	`scopes` text,
	`status` text DEFAULT 'active',
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	`token_expires_at` integer,
	`last_checked_at` integer,
	`last_polled_at` integer,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_platform_connections`("id", "project_id", "platform", "platform_account_id", "platform_account_name", "access_token_encrypted", "refresh_token_encrypted", "scopes", "status", "created_at", "token_expires_at", "last_checked_at", "last_polled_at") SELECT "id", "project_id", "platform", "platform_account_id", "platform_account_name", "access_token_encrypted", "refresh_token_encrypted", "scopes", "status", "created_at", "token_expires_at", "last_checked_at", "last_polled_at" FROM `platform_connections`;--> statement-breakpoint
DROP TABLE `platform_connections`;--> statement-breakpoint
ALTER TABLE `__new_platform_connections` RENAME TO `platform_connections`;--> statement-breakpoint
CREATE INDEX `connection_feed_idx` ON `platform_connections` (`project_id`);--> statement-breakpoint
CREATE TABLE `__new_posts` (
	`platform_connection_id` text NOT NULL,
	`media_id` text NOT NULL,
	`like_count` integer DEFAULT 0,
	`comments_count` integer DEFAULT 0,
	`caption` text,
	`media_url` text,
	`media_type` text,
	`permalink` text,
	`timestamp` integer,
	`last_webhook_update` integer,
	`last_fetched` integer,
	PRIMARY KEY(`platform_connection_id`, `media_id`),
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_posts`("platform_connection_id", "media_id", "like_count", "comments_count", "caption", "media_url", "media_type", "permalink", "timestamp", "last_webhook_update", "last_fetched") SELECT "platform_connection_id", "media_id", "like_count", "comments_count", "caption", "media_url", "media_type", "permalink", "timestamp", "last_webhook_update", "last_fetched" FROM `posts`;--> statement-breakpoint
DROP TABLE `posts`;--> statement-breakpoint
ALTER TABLE `__new_posts` RENAME TO `posts`;--> statement-breakpoint
CREATE INDEX `connection_timestamp_idx` ON `posts` (`platform_connection_id`,`timestamp`);--> statement-breakpoint
DROP INDEX `apikey_feed_idx`;--> statement-breakpoint
CREATE INDEX `apikey_feed_idx` ON `api_keys` (`feed_id`);--> statement-breakpoint
ALTER TABLE `feeds` ADD `project_id` text DEFAULT 'default-project' NOT NULL REFERENCES projects(id);--> statement-breakpoint
CREATE INDEX `feed_project_idx` ON `feeds` (`project_id`);--> statement-breakpoint
ALTER TABLE `feeds` DROP COLUMN `organization_id`;--> statement-breakpoint
ALTER TABLE `feeds` DROP COLUMN `platforms`;