{"name": "@repo/api", "version": "0.2.0", "private": true, "scripts": {"dev": "wrangler dev src/index.ts", "deploy": "wrangler deploy src/index.ts", "db:generate": "drizzle-kit generate", "db:migrate:local": "wrangler d1 migrations apply socialfeed-test --local", "db:migrate:prod": "wrangler d1 migrations apply socialfeed-test --remote", "logs": "wrangler tail", "check-types": "tsc --noEmit"}, "dependencies": {"@propelauth/node": "^2.1.33", "@repo/drizzle-schema": "workspace:*", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "hono": "^4.7.7", "jose": "^5.10.0", "zod": "^3.25.67"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250618.0", "@types/node": "^20.17.31", "drizzle-kit": "^0.31.1", "typescript": "^5.8.3", "wrangler": "^4.20.3"}, "module": "src/index.ts"}