"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "../ui/dialog";
import { toast } from "../ui/use-toast";
import { But<PERSON> } from "../ui/button";
import { Plus } from "lucide-react";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { availableNetworks } from "@/constants/availableNetworks";
import { createProject } from "@/lib/actions/createProject";

const CreateProjectButton: React.FC = () => {
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false);

  const [newProjectName, setNewProjectName] = useState("");
  const [newProjectDescription, setNewProjectDescription] = useState("");
  const [selectedNetworks, setSelectedNetworks] = useState<string[]>([
    "facebook",
    "instagram",
  ]);

  const handleNetworkToggle = (networkSlug: string) => {
    setSelectedNetworks((prev) =>
      prev.includes(networkSlug)
        ? prev.filter((slug) => slug !== networkSlug)
        : [...prev, networkSlug]
    );
  };

  // Die Struktur hat sich geändert. Wir Früher hier project in der DB Feed,
  // jetzt liegt in der DB Projekt über feed. Wir müssen also jetzt ein Projekt erstellen,
  // dafür gibt es noch keine Funktuion

  const handleCreateProject = async () => {
    const feedId = await createProject(
      newProjectName,
      newProjectDescription,
      projectId
    );

    if (feedId) {
      toast({
        title: "Project created",
        description: `New project "${newProjectName}" has been created with ${selectedNetworks.length} networks activated.`,
      });
      setIsCreateProjectOpen(false);
      setNewProjectName("");
      setNewProjectDescription("");
      setSelectedNetworks(["facebook", "instagram"]);
    }
  };
  return (
    <Dialog open={isCreateProjectOpen} onOpenChange={setIsCreateProjectOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Project
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Create a new social media analytics project and select which
            networks to activate.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-2">
            <Label htmlFor="project-name">Project Name</Label>
            <Input
              id="project-name"
              placeholder="e.g., Brand Analytics"
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="project-description">Description</Label>
            <Input
              id="project-description"
              placeholder="Brief description of your project"
              value={newProjectDescription}
              onChange={(e) => setNewProjectDescription(e.target.value)}
            />
          </div>
          <div className="grid gap-3">
            <Label>Networks to Activate</Label>
            <p className="text-sm text-muted-foreground">
              Select which social media networks you want to enable for this
              project. You can change this later.
            </p>
            <div className="grid gap-3 md:grid-cols-2">
              {availableNetworks.map((network) => {
                const IconComponent = network.icon;
                return (
                  <div
                    key={network.slug}
                    className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedNetworks.includes(network.slug)
                        ? "border-primary bg-primary/5"
                        : "border-border hover:bg-muted/50"
                    }`}
                    onClick={() => handleNetworkToggle(network.slug)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedNetworks.includes(network.slug)}
                      onChange={() => handleNetworkToggle(network.slug)}
                      className="rounded"
                    />
                    <IconComponent className="h-5 w-5" />
                    <div className="flex-1">
                      <div className="font-medium">{network.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {network.description}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreateProjectOpen(false)}
          >
            Cancel
          </Button>
          <Button onClick={handleCreateProject} disabled={!newProjectName}>
            Create Project
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateProjectButton;
