import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "../ui/card";
import { Building } from "lucide-react";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";

const getFeedCount = async () => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const org = user.getActiveOrg();
  const orgId = org?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  // --- DER MAGISCHE TEIL: Typsicherer API-Aufruf ---
  const res = await apiClient.manage.feed.count.$get();
  const data = await res.json();
  return data;
};

const TotalProjectsCard: React.FC = async () => {
  const count = await getFeedCount();
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
        <Building className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count.feeds}</div>
        <p className="text-xs text-muted-foreground">
          {count.activeFeeds} active
        </p>
      </CardContent>
    </Card>
  );
};

export default TotalProjectsCard;
