import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "../ui/card";
import { BarChart3, DollarSign } from "lucide-react";

const TotalCostCard = () => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
        <DollarSign className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">${(0).toFixed(2)}</div>
        <p className="text-xs text-muted-foreground">
          This month (updated hourly)
        </p>
      </CardContent>
    </Card>
  );
};

export default TotalCostCard;
