"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import {
  ArrowLeft,
  Bell,
  Facebook,
  Instagram,
  Mail,
  Plus,
  Save,
  Settings,
  Trash2,
  Twitter,
  Youtube,
  X,
  AlertTriangle,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";

// Mock data
const projectSettings = {
  name: "PLAZE Feed",
  id: "proj_abc123",
  description:
    "Social media analytics and data collection for e-commerce insights",
  createdAt: "2023-10-15",
};

const socialNetworkSettings = [
  {
    name: "Facebook",
    icon: Facebook,
    enabled: true,
    connected: true,
    description: "Access Facebook pages, posts, and insights",
  },
  {
    name: "Instagram",
    icon: Instagram,
    enabled: true,
    connected: true,
    description: "Collect Instagram posts, stories, and engagement data",
  },
  {
    name: "Twitter",
    icon: Twitter,
    enabled: false,
    connected: false,
    description: "Monitor tweets, mentions, and trending topics",
  },
  {
    name: "YouTube",
    icon: Youtube,
    enabled: true,
    connected: true,
    description: "Analyze video content, comments, and channel metrics",
  },
];

const notificationEmails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export default function ProjectSettingsPage() {
  const params = useParams();
  const projectId = params.id as string;

  const [projectName, setProjectName] = useState(projectSettings.name);
  const [projectDescription, setProjectDescription] = useState(
    projectSettings.description
  );
  const [socialSettings, setSocialSettings] = useState(socialNetworkSettings);
  const [emails, setEmails] = useState(notificationEmails);
  const [newEmail, setNewEmail] = useState("");
  const [isAddEmailOpen, setIsAddEmailOpen] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleProjectNameChange = (value: string) => {
    setProjectName(value);
    setHasUnsavedChanges(true);
  };

  const handleProjectDescriptionChange = (value: string) => {
    setProjectDescription(value);
    setHasUnsavedChanges(true);
  };

  const handleSocialToggle = (networkName: string, enabled: boolean) => {
    setSocialSettings((prev) =>
      prev.map((network) =>
        network.name === networkName ? { ...network, enabled } : network
      )
    );
    setHasUnsavedChanges(true);
  };

  const handleAddEmail = () => {
    if (newEmail && !emails.includes(newEmail)) {
      setEmails((prev) => [...prev, newEmail]);
      setNewEmail("");
      setIsAddEmailOpen(false);
      setHasUnsavedChanges(true);
      toast({
        title: "Email added",
        description: "Notification email has been added successfully.",
      });
    }
  };

  const handleRemoveEmail = (emailToRemove: string) => {
    setEmails((prev) => prev.filter((email) => email !== emailToRemove));
    setHasUnsavedChanges(true);
    toast({
      title: "Email removed",
      description: "Notification email has been removed.",
    });
  };

  const handleSaveSettings = () => {
    // Mock save logic
    setHasUnsavedChanges(false);
    toast({
      title: "Settings saved",
      description: "All project settings have been updated successfully.",
    });
  };

  const handleDeleteProject = () => {
    // Mock delete logic
    toast({
      title: "Project deleted",
      description: "The project has been permanently deleted.",
      variant: "destructive",
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <Link href={`/dashboard/projects/${projectId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Project
              </Button>
            </Link>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-3">
              <Settings className="h-5 w-5" />
              <h1 className="text-xl font-semibold">Project Settings</h1>
              <Badge variant="outline">{projectSettings.id}</Badge>
            </div>
          </div>
          <div className="ml-auto">
            <Button
              onClick={handleSaveSettings}
              disabled={!hasUnsavedChanges}
              className="mr-2"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            {hasUnsavedChanges && (
              <Badge variant="secondary">Unsaved changes</Badge>
            )}
          </div>
        </div>
      </div>

      <div className="p-6 max-w-4xl mx-auto space-y-6">
        {/* Project Information */}
        <Card>
          <CardHeader>
            <CardTitle>Project Information</CardTitle>
            <CardDescription>
              Update your project name and description.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="project-name">Project Name</Label>
              <Input
                id="project-name"
                value={projectName}
                onChange={(e) => handleProjectNameChange(e.target.value)}
                placeholder="Enter project name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="project-description">Description</Label>
              <Textarea
                id="project-description"
                value={projectDescription}
                onChange={(e) => handleProjectDescriptionChange(e.target.value)}
                placeholder="Enter project description"
                rows={3}
              />
            </div>
            <div className="text-sm text-muted-foreground">
              Created on{" "}
              {new Date(projectSettings.createdAt).toLocaleDateString()}
            </div>
          </CardContent>
        </Card>

        {/* Social Media Networks */}
        <Card>
          <CardHeader>
            <CardTitle>Social Media Networks</CardTitle>
            <CardDescription>
              Enable or disable social media networks for this project. Disabled
              networks will stop collecting data.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {socialSettings.map((network) => {
                const IconComponent = network.icon;
                return (
                  <div
                    key={network.name}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <IconComponent className="h-8 w-8" />
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{network.name}</h3>
                          {network.connected && (
                            <Badge variant="outline" className="text-xs">
                              Connected
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {network.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label
                        htmlFor={`${network.name}-toggle`}
                        className="text-sm"
                      >
                        {network.enabled ? "Enabled" : "Disabled"}
                      </Label>
                      <Switch
                        id={`${network.name}-toggle`}
                        checked={network.enabled}
                        onCheckedChange={(checked) =>
                          handleSocialToggle(network.name, checked)
                        }
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Bell className="h-5 w-5" />
                  <span>Notification Settings</span>
                </CardTitle>
                <CardDescription>
                  Manage email addresses that should be notified when social
                  media connections need to be reconnected.
                </CardDescription>
              </div>
              <Dialog open={isAddEmailOpen} onOpenChange={setIsAddEmailOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Email
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Notification Email</DialogTitle>
                    <DialogDescription>
                      Add an email address to receive notifications when
                      connections need attention.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddEmailOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddEmail} disabled={!newEmail}>
                      Add Email
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {emails.map((email, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{email}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              {emails.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No notification emails configured
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Danger Zone */}
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              <span>Danger Zone</span>
            </CardTitle>
            <CardDescription>
              Irreversible and destructive actions for this project.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border border-destructive rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-destructive">
                      Delete Project
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Permanently delete this project and all associated data.
                      This action cannot be undone.
                    </p>
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Project
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Are you absolutely sure?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently
                          delete the project "{projectName}" and remove all
                          associated data including:
                          <ul className="list-disc list-inside mt-2 space-y-1">
                            <li>All social media connections</li>
                            <li>Generated connection links</li>
                            <li>API usage history and analytics</li>
                            <li>Project settings and configurations</li>
                          </ul>
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteProject}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Yes, delete project
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
