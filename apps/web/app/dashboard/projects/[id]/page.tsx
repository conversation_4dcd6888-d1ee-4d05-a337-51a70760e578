"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import {
  BarChart3,
  Calendar,
  Copy,
  DollarSign,
  Edit,
  ExternalLink,
  Facebook,
  Instagram,
  Link2,
  Plus,
  Settings,
  TrendingUp,
  Twitter,
  Youtube,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Unlink,
  TestTube,
  Save,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";
import DashboardHeader from "@/components/primitives/DashboardHeader";

// Mock data - in real app, this would be fetched based on project ID
const projectData = {
  name: "PLAZE Feed",
  id: "proj_abc123",
  totalRequests: 45672,
  totalCost: 234.56,
  monthlyRequests: 12543,
  monthlyCost: 67.89,
};

const socialNetworks = [
  {
    name: "Facebook",
    slug: "facebook",
    icon: Facebook,
    connected: true,
    status: "active",
    lastSync: "2 hours ago",
    connectedAccount: {
      id: "fb_001",
      name: "@company_page",
      type: "Page",
      followers: "12.5K",
    },
    permissions: ["pages_read_engagement", "pages_show_list"],
    dataCollected: 1250,
  },
  {
    name: "Instagram",
    slug: "instagram",
    icon: Instagram,
    connected: true,
    status: "active",
    lastSync: "1 hour ago",
    connectedAccount: {
      id: "ig_001",
      name: "@company_insta",
      type: "Business",
      followers: "25.3K",
    },
    permissions: ["instagram_basic", "instagram_manage_insights"],
    dataCollected: 890,
  },
  {
    name: "Twitter",
    slug: "twitter",
    icon: Twitter,
    connected: false,
    status: "disconnected",
    lastSync: "Never",
    connectedAccount: null,
    permissions: [],
    dataCollected: 0,
  },
  {
    name: "YouTube",
    slug: "youtube",
    icon: Youtube,
    connected: true,
    status: "error",
    lastSync: "3 days ago",
    connectedAccount: {
      id: "yt_001",
      name: "Company Channel",
      type: "Channel",
      followers: "45.2K",
    },
    permissions: ["youtube.readonly"],
    dataCollected: 456,
  },
];

const generatedLinks = [
  {
    id: "link_001",
    name: "Client Demo Access",
    url: "https://api.example.com/connect/abc123def",
    expiresAt: "2024-01-15",
    status: "active",
  },
  {
    id: "link_002",
    name: "Partner Integration",
    url: "https://api.example.com/connect/xyz789ghi",
    expiresAt: "2024-01-20",
    status: "active",
  },
  {
    id: "link_003",
    name: "Testing Environment",
    url: "https://api.example.com/connect/test456jkl",
    expiresAt: "2023-12-28",
    status: "expired",
  },
];

export default function ProjectDashboard() {
  const params = useParams();
  const projectId = params.id as string;

  const [isCreateLinkOpen, setIsCreateLinkOpen] = useState(false);
  const [isManageNetworkOpen, setIsManageNetworkOpen] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState<
    (typeof socialNetworks)[0] | null
  >(null);
  const [newLinkName, setNewLinkName] = useState("");
  const [newLinkExpiry, setNewLinkExpiry] = useState("");
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [editingLink, setEditingLink] = useState<
    (typeof generatedLinks)[0] | null
  >(null);
  const [editLinkName, setEditLinkName] = useState("");
  const [editLinkExpiry, setEditLinkExpiry] = useState("");
  const [openEditPopover, setOpenEditPopover] = useState<string | null>(null);

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url);
    toast({
      title: "Link copied",
      description: "The connection link has been copied to your clipboard.",
    });
  };

  const handleCreateLink = () => {
    // Mock creation logic
    toast({
      title: "Link created",
      description: "New connection link has been generated successfully.",
    });
    setIsCreateLinkOpen(false);
    setNewLinkName("");
    setNewLinkExpiry("");
  };

  const handleEditLink = (link: (typeof generatedLinks)[0]) => {
    setEditingLink(link);
    setEditLinkName(link.name);
    setEditLinkExpiry(link.expiresAt);
    setOpenEditPopover(link.id);
  };

  const handleSaveEdit = () => {
    if (!editingLink) return;

    // Mock save logic
    toast({
      title: "Link updated",
      description: "The connection link has been updated successfully.",
    });

    setOpenEditPopover(null);
    setEditingLink(null);
    setEditLinkName("");
    setEditLinkExpiry("");
  };

  const handleCancelEdit = () => {
    setOpenEditPopover(null);
    setEditingLink(null);
    setEditLinkName("");
    setEditLinkExpiry("");
  };

  const handleManageNetwork = (network: (typeof socialNetworks)[0]) => {
    setSelectedNetwork(network);
    setIsManageNetworkOpen(true);
  };

  const handleTestConnection = async () => {
    if (!selectedNetwork) return;
    setIsTestingConnection(true);
    // Mock testing delay
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsTestingConnection(false);
    toast({
      title: "Connection tested",
      description: `${selectedNetwork.name} connection is working properly.`,
    });
  };

  const handleReconnect = () => {
    if (!selectedNetwork) return;
    toast({
      title: "Reconnection initiated",
      description: `Starting reconnection process for ${selectedNetwork.name}.`,
    });
    setIsManageNetworkOpen(false);
  };

  const handleDisconnectAccount = (accountId: string, accountName: string) => {
    toast({
      title: "Account disconnected",
      description: `${accountName} has been disconnected.`,
      variant: "destructive",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "expired":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      error: "destructive",
      expired: "secondary",
      disconnected: "outline",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="px-4">
        <DashboardHeader
          title={projectData.name}
          id={projectData.id}
          link={{ label: "All Projects", href: "/dashboard/projects" }}
        >
          <div className="ml-auto flex items-center space-x-4">
            <Link href={`/dashboard/projects/${projectId}/api-settings`}>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                API Settings
              </Button>
            </Link>
            <Link href={`/dashboard/projects/${projectId}/settings`}>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </DashboardHeader>
      </div>

      <div className="p-4 space-y-6">
        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Requests
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {projectData.totalRequests.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                +{projectData.monthlyRequests.toLocaleString()} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${projectData.totalCost}</div>
              <p className="text-xs text-muted-foreground">
                +${projectData.monthlyCost} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Connected Networks
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {socialNetworks.filter((n) => n.connected).length}/
                {socialNetworks.length}
              </div>
              <p className="text-xs text-muted-foreground">
                {socialNetworks.filter((n) => n.status === "active").length}{" "}
                active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Links
              </CardTitle>
              <Link2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {generatedLinks.filter((l) => l.status === "active").length}
              </div>
              <p className="text-xs text-muted-foreground">
                {generatedLinks.filter((l) => l.status === "expired").length}{" "}
                expired
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="networks" className="space-y-4">
          <TabsList>
            <TabsTrigger value="networks">Social Networks</TabsTrigger>
            <TabsTrigger value="links">Generated Links</TabsTrigger>
          </TabsList>

          <TabsContent value="networks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Networks</CardTitle>
                <CardDescription>
                  Manage your social media network connections and monitor their
                  status.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {socialNetworks.map((network) => {
                    const IconComponent = network.icon;
                    return (
                      <Card key={network.name}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <IconComponent className="h-8 w-8" />
                              <div>
                                <h3 className="font-medium">{network.name}</h3>
                                <p className="text-sm text-muted-foreground">
                                  {network.connected && network.connectedAccount
                                    ? network.connectedAccount.name
                                    : "Not connected"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(network.status)}
                              {getStatusBadge(network.status)}
                            </div>
                          </div>
                          <div className="mt-4 flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              Last sync: {network.lastSync}
                            </span>
                            {network.connected ? (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleManageNetwork(network)}
                              >
                                Manage
                              </Button>
                            ) : (
                              <Link href={`/connect/${network.slug}`}>
                                <Button size="sm">Connect</Button>
                              </Link>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="links" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Generated Links</CardTitle>
                    <CardDescription>
                      Manage connection links for external users to connect
                      their social media accounts.
                    </CardDescription>
                  </div>
                  <Dialog
                    open={isCreateLinkOpen}
                    onOpenChange={setIsCreateLinkOpen}
                  >
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Link
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Connection Link</DialogTitle>
                        <DialogDescription>
                          Generate a new link for external users to connect
                          their social media accounts.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                          <Label htmlFor="name">Link Name</Label>
                          <Input
                            id="name"
                            placeholder="e.g., Client Demo Access"
                            value={newLinkName}
                            onChange={(e) => setNewLinkName(e.target.value)}
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="expiry">Expiry Date</Label>
                          <Input
                            id="expiry"
                            type="date"
                            value={newLinkExpiry}
                            onChange={(e) => setNewLinkExpiry(e.target.value)}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsCreateLinkOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleCreateLink}>Create Link</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {generatedLinks.map((link) => (
                      <TableRow key={link.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{link.name}</div>
                            <div className="text-sm text-muted-foreground font-mono">
                              {link.url.substring(0, 40)}...
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(link.status)}
                            {getStatusBadge(link.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{link.expiresAt}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCopyLink(link.url)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Popover
                              open={openEditPopover === link.id}
                              onOpenChange={(open) => {
                                if (!open) {
                                  handleCancelEdit();
                                }
                              }}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleEditLink(link)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-80">
                                <div className="space-y-4">
                                  <div className="space-y-2">
                                    <h4 className="font-medium leading-none">
                                      Edit Link
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                      Update the link name and expiry date.
                                    </p>
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="edit-name">Link Name</Label>
                                    <Input
                                      id="edit-name"
                                      value={editLinkName}
                                      onChange={(e) =>
                                        setEditLinkName(e.target.value)
                                      }
                                      placeholder="Enter link name"
                                    />
                                  </div>
                                  <div className="grid gap-2">
                                    <Label htmlFor="edit-expiry">
                                      Expiry Date
                                    </Label>
                                    <Input
                                      id="edit-expiry"
                                      type="date"
                                      value={editLinkExpiry}
                                      onChange={(e) =>
                                        setEditLinkExpiry(e.target.value)
                                      }
                                    />
                                  </div>
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={handleCancelEdit}
                                    >
                                      Cancel
                                    </Button>
                                    <Button size="sm" onClick={handleSaveEdit}>
                                      <Save className="h-4 w-4 mr-2" />
                                      Save
                                    </Button>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                            <Link
                              href={`/link/${link.url.split("/").pop()}`}
                              target="_blank"
                            >
                              <Button size="sm" variant="outline">
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Social Media Network Management Dialog */}
      <Dialog open={isManageNetworkOpen} onOpenChange={setIsManageNetworkOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {selectedNetwork && (
                <>
                  <selectedNetwork.icon className="h-6 w-6" />
                  <span>Manage {selectedNetwork.name}</span>
                  {getStatusBadge(selectedNetwork.status)}
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              Manage your {selectedNetwork?.name} connection, accounts, and
              settings.
            </DialogDescription>
          </DialogHeader>

          {selectedNetwork && (
            <div className="space-y-6">
              {/* Connection Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Connection Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-muted-foreground">
                        Status
                      </Label>
                      <div className="flex items-center space-x-2 mt-1">
                        {getStatusIcon(selectedNetwork.status)}
                        <span className="font-medium">
                          {selectedNetwork.status}
                        </span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">
                        Last Sync
                      </Label>
                      <p className="font-medium mt-1">
                        {selectedNetwork.lastSync}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={handleTestConnection}
                      disabled={
                        isTestingConnection ||
                        selectedNetwork.status === "disconnected"
                      }
                    >
                      {isTestingConnection ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4 mr-2" />
                      )}
                      Test Connection
                    </Button>
                    {selectedNetwork.status === "error" && (
                      <Button onClick={handleReconnect}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Reconnect
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Connected Accounts */}
              {selectedNetwork.connectedAccount && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Connected Account</CardTitle>
                    <CardDescription>
                      Manage the social media account connected to this
                      platform.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div
                        key={selectedNetwork.connectedAccount.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <selectedNetwork.icon className="h-5 w-5" />
                          <div>
                            <div className="font-medium">
                              {selectedNetwork.connectedAccount.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {selectedNetwork.connectedAccount.type} •{" "}
                              {selectedNetwork.connectedAccount.followers}{" "}
                              followers
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            Active
                          </Badge>
                          <Button size="sm" variant="outline">
                            Replace Account
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="outline">
                                <Unlink className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Disconnect Account
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will disconnect "
                                  {selectedNetwork.connectedAccount.name}" from
                                  your project. You will stop receiving data
                                  from this account.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() =>
                                    handleDisconnectAccount(
                                      selectedNetwork.connectedAccount.id,
                                      selectedNetwork.connectedAccount.name
                                    )
                                  }
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Disconnect
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Permissions */}
              {selectedNetwork.permissions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Permissions</CardTitle>
                    <CardDescription>
                      Current permissions granted for this platform.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedNetwork.permissions.map((permission) => (
                        <Badge key={permission} variant="secondary">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsManageNetworkOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
