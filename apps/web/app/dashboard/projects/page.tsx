import { Calendar, Clock, ExternalLink } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import StatusIcon from "@/components/primitives/StatusIcon";
import StatusBadge from "@/components/primitives/StatusBadge";
import DashboardHeader from "@/components/primitives/DashboardHeader";
import TotalProjectsCard from "@/components/dashboard/TotalProjectsCard";
import TotalRequestsCard from "@/components/dashboard/TotalRequestsCard";
import TotalCostCard from "@/components/dashboard/TotalCostCard";
import TotalConnectedNetworks from "@/components/dashboard/TotalConnectedNetworks";
import ProtectedRoute from "@/lib/auth/ProtectedRoute";
import CreateProjectButton from "@/components/dashboard/CreateProjectButton";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import Link from "next/link";
import { Button } from "@/components/ui/button";

const getProjects = async () => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects.$get();
  const projects = await res.json();
  return projects;
};

export default async function ProjectsOverview() {
  const proj = await getProjects();

  console.log(proj);

  return (
    <ProtectedRoute>
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <DashboardHeader title="Projects" />
        <div className="flex-1 space-y-6">
          {/* Account Overview Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <TotalProjectsCard />
            <TotalRequestsCard />
            <TotalCostCard />
            <TotalConnectedNetworks />
          </div>
          {/* Projects List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Your Projects</CardTitle>
                  <CardDescription>
                    Manage your social media analytics projects and monitor
                    their performance.
                  </CardDescription>
                </div>
                {/*}
                <CreateProjectButton />
                {*/}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {proj.map((project) => {
                  const networks = JSON.parse(project.platforms);
                  const activatedNetworks = networks.filter(
                    (n: any) => n.status === "active"
                  );
                  return (
                    <Card
                      key={project.id}
                      className="hover:shadow-md transition-shadow"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-3 flex-1">
                            <div className="flex items-center space-x-3">
                              <h3 className="text-lg font-semibold">
                                {project.name}
                              </h3>
                              <StatusIcon status={project.status} />
                              <StatusBadge status={project.status} />
                            </div>
                            <p className="text-muted-foreground">
                              {project.description}
                            </p>

                            {/* Network Status Indicators */}
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-muted-foreground">
                                  Networks:
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  {activatedNetworks.length} of{" "}
                                  {networks.length} activated
                                </span>
                              </div>
                              {/* <NetworkIndicators project={project} /> */}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-4 w-4" />
                                {project.createdAt && (
                                  <span>
                                    Created{" "}
                                    {new Date(
                                      project.createdAt
                                    ).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4" />
                                <span>
                                  Last activity{" "}
                                  {project.lastActivity ?? "never"}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Link href={`/dashboard/projects/${project.id}`}>
                              <Button>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Open Project
                              </Button>
                            </Link>
                          </div>
                        </div>

                        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold">
                              {activatedNetworks.length}/{networks.length}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Networks
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
